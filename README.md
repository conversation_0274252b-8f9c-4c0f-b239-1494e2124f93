# SyncLounge

A self-hosted video synchronization application that allows multiple users to watch videos together in real-time.

## Features

*   **Self-hosted:** Full control over your data and infrastructure.
*   **Real-time Sync:** Synchronized video playback for all users in a room.
*   **User Authentication:** Secure user registration and login system.
*   **Admin Panel:** The first registered user becomes the administrator.
*   **HLS Transcoding:** On-the-fly video transcoding using `ffmpeg` for smooth streaming.

## Prerequisites

*   **Go:** Version 1.18 or higher.
*   **ffmpeg:** Must be installed and available in the system's PATH.

## How to Run

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/synclounge.git
    cd synclounge
    ```

2.  **Build the application:**
    Run the build script to compile the Go binary.
    ```bash
    ./scripts/build.sh
    ```

3.  **Run the server:**
    Start the server and point it to your media directory.
    ```bash
    ./synclounge -media-dir /path/to/your/videos
    ```
    On the first run, the first user to register will be granted administrator privileges.

## API Endpoints

### HTTP API

*   `POST /api/users/register` - Register a new user.
*   `POST /api/users/login` - Login a user.
*   `GET /api/videos` - Get a list of available videos.
*   `POST /api/rooms` - Create a new room.
*   `GET /api/rooms` - Get a list of active rooms.

### WebSocket

*   `GET /ws/room/{roomId}` - WebSocket connection for a specific room.