<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SyncLounge</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
</head>
<body class="bg-gray-900 text-white">

    <main id="app-root">
        <!-- Login/Register View -->
        <section id="auth-view" class="p-8 max-w-md mx-auto">
            <h1 class="text-3xl font-bold mb-6 text-center">SyncLounge</h1>
            <div id="auth-container">
                <!-- Login Form -->
                <form id="login-form" class="space-y-4">
                    <h2 class="text-2xl font-semibold">Login</h2>
                    <div>
                        <label for="login-username" class="block mb-2">Username</label>
                        <input type="text" id="login-username" name="username" required class="w-full p-2 bg-gray-800 border border-gray-700 rounded">
                    </div>
                    <div>
                        <label for="login-password" class="block mb-2">Password</label>
                        <input type="password" id="login-password" name="password" required class="w-full p-2 bg-gray-800 border border-gray-700 rounded">
                    </div>
                    <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 p-2 rounded">Login</button>
                    <p class="text-center">Don't have an account? <a href="#" id="show-register" class="text-indigo-400 hover:underline">Register</a></p>
                </form>

                <!-- Register Form (hidden by default) -->
                <form id="register-form" class="space-y-4 hidden">
                    <h2 class="text-2xl font-semibold">Register</h2>
                    <div>
                        <label for="register-username" class="block mb-2">Username</label>
                        <input type="text" id="register-username" name="username" required class="w-full p-2 bg-gray-800 border border-gray-700 rounded">
                    </div>
                    <div>
                        <label for="register-password" class="block mb-2">Password</label>
                        <input type="password" id="register-password" name="password" required class="w-full p-2 bg-gray-800 border border-gray-700 rounded">
                    </div>
                    <button type="submit" class="w-full bg-green-600 hover:bg-green-700 p-2 rounded">Register</button>
                    <p class="text-center">Already have an account? <a href="#" id="show-login" class="text-indigo-400 hover:underline">Login</a></p>
                </form>
            </div>
        </section>

        <!-- Pending Verification View -->
        <section id="pending-view" class="hidden p-8 text-center">
            <h1 class="text-3xl font-bold mb-4">Pending Verification</h1>
            <p>Your account is awaiting admin approval. Please check back later.</p>
            <button id="logout-pending" class="mt-4 bg-red-600 hover:bg-red-700 p-2 rounded">Logout</button>
        </section>

        <!-- Admin Panel View -->
        <section id="admin-view" class="hidden p-8">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold">Admin Panel</h1>
                <button id="logout-admin" class="bg-red-600 hover:bg-red-700 p-2 rounded">Logout</button>
            </div>
            <div id="user-list" class="space-y-4"></div>
        </section>

        <!-- Media Library View -->
        <section id="library-view" class="hidden p-8">
             <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold">Media Library</h1>
                <div>
                    <input type="text" id="join-room-id" placeholder="Enter Room ID" class="p-2 bg-gray-800 border border-gray-700 rounded">
                    <button id="join-room-btn" class="bg-blue-600 hover:bg-blue-700 p-2 rounded ml-2">Join Room</button>
                    <button id="logout-library" class="bg-red-600 hover:bg-red-700 p-2 rounded ml-4">Logout</button>
                </div>
            </div>
            <div id="video-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"></div>
        </section>

        <!-- Watch Room View -->
        <section id="room-view" class="hidden h-screen flex flex-col">
            <div class="flex-grow flex">
                <div class="flex-grow flex flex-col">
                    <div class="bg-black flex-grow relative">
                        <video id="video-player" class="w-full h-full" controls></video>
                    </div>
                    <div class="p-4 bg-gray-800">
                        <h2 id="room-title" class="text-2xl font-bold"></h2>
                    </div>
                </div>
                <div class="w-80 bg-gray-800 flex flex-col p-4 space-y-4">
                    <div>
                        <h3 class="text-xl font-semibold mb-2">Users</h3>
                        <ul id="user-list-room" class="space-y-2"></ul>
                    </div>
                    <div class="flex-grow flex flex-col">
                        <h3 class="text-xl font-semibold mb-2">Chat</h3>
                        <div id="chat-messages" class="flex-grow bg-gray-900 rounded p-2 overflow-y-auto mb-2"></div>
                        <form id="chat-form" class="flex">
                            <input type="text" id="chat-input" class="flex-grow p-2 bg-gray-700 border border-gray-600 rounded-l" placeholder="Say something...">
                            <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 p-2 rounded-r">Send</button>
                        </form>
                    </div>
                     <div>
                        <button id="leave-room-btn" class="w-full bg-red-600 hover:bg-red-700 p-2 rounded">Leave Room</button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="/static/js/app.js"></script>
</body>
</html>