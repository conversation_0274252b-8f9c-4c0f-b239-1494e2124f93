package main

import (
	"flag"
	"log"

	"synclounge/server/internal/api/handlers"
	"synclounge/server/internal/api/middleware"
	"synclounge/server/internal/core/services"
	"synclounge/server/internal/core/websocket"
	"synclounge/server/internal/database"

	"github.com/gin-gonic/gin"
)

var mediaDir = flag.String("media-dir", "./media", "the directory containing media files")

func main() {
	flag.Parse()
	log.Println("Starting SyncLounge server...")

	// Initialize the database
	database.InitDB("synclounge.db")
	db := database.DB

	// Create the user service and handler
	userService := services.NewUserService(db)
	userHandler := handlers.NewUserHandler(userService)

	// Create the video service and handler
	videoService := services.NewVideoService()
	videoHandler := handlers.NewVideoHandler(videoService)

	// Scan the media library in the background
	go videoService.ScanLibrary(*mediaDir)

	// Create a new Gin router
	router := gin.Default()

	// Initialize the websocket hub
	hub := websocket.NewHub()
	go hub.Run()

	// Create the room service and handler
	roomService := services.NewRoomService(hub, videoService)
	roomHandler := handlers.NewRoomHandler(roomService, hub)

	// Serve static files and templates
	router.Static("/static", "./web/static")
	router.LoadHTMLGlob("web/templates/*")

	// Frontend routes
	router.GET("/", roomHandler.ServeIndexHandler)
	router.GET("/login", roomHandler.ServeIndexHandler)
	router.GET("/register", roomHandler.ServeIndexHandler)
	router.GET("/rooms/:roomID", roomHandler.ServeIndexHandler)

	// Websocket route
	router.GET("/ws/rooms/:roomID", roomHandler.JoinRoomHandler)

	// Public routes
	router.POST("/register", userHandler.RegisterUser)
	router.POST("/login", userHandler.LoginUser)

	// HLS streaming routes (public for easier video player access)
	router.GET("/api/stream/:videoID/playlist.m3u8", videoHandler.StreamVideoHandler)
	router.GET("/api/stream/:videoID/:segment", videoHandler.ServeHlsSegment)

	// Authenticated routes
	apiRoutes := router.Group("/api")
	apiRoutes.Use(middleware.JWTAuthMiddleware())
	{
		apiRoutes.GET("/library", videoHandler.GetMediaLibrary)
		apiRoutes.POST("/rooms", roomHandler.CreateRoomHandler)
		apiRoutes.GET("/rooms/:roomId", roomHandler.GetRoomHandler)
		apiRoutes.GET("/users/me", userHandler.GetCurrentUser)
	}

	// Admin routes
	adminRoutes := router.Group("/api/admin")
	adminRoutes.Use(middleware.JWTAuthMiddleware())
	adminRoutes.Use(middleware.RequireAdmin())
	{
		adminRoutes.GET("/users", userHandler.GetUsers)
		adminRoutes.POST("/users/:userID/approve", userHandler.ApproveUser)
		adminRoutes.DELETE("/users/:userID", userHandler.DenyUser)
	}

	// Start the server
	log.Println("Server is listening on port 8080")
	if err := router.Run(":8080"); err != nil {
		log.Fatalf("Could not start server: %s\n", err)
	}
}
