package services

import (
	"database/sql"
	"errors"

	"synclounge/server/internal/core/models"

	"golang.org/x/crypto/bcrypt"
)

type UserService struct {
	DB *sql.DB
}

func NewUserService(db *sql.DB) *UserService {
	return &UserService{DB: db}
}

func (s *UserService) CreateUser(username, password string) (*models.User, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	user := &models.User{
		Username:   username,
		Password:   string(hashedPassword),
		Role:       "user",
		IsVerified: false,
	}

	// Determine if this is the first user
	var userCount int
	err = s.DB.QueryRow("SELECT COUNT(*) FROM users").Scan(&userCount)
	if err != nil {
		return nil, err
	}

	if userCount == 0 {
		user.Role = "admin"
		user.IsVerified = true
	}

	stmt, err := s.DB.Prepare("INSERT INTO users(username, password, role, is_verified) VALUES(?, ?, ?, ?)")
	if err != nil {
		return nil, err
	}
	defer stmt.Close()

	res, err := stmt.Exec(user.Username, user.Password, user.Role, user.IsVerified)
	if err != nil {
		return nil, err
	}

	id, err := res.LastInsertId()
	if err != nil {
		return nil, err
	}
	user.ID = int(id)

	return user, nil
}

func (s *UserService) GetUserByUsername(username string) (*models.User, error) {
	user := &models.User{}
	err := s.DB.QueryRow("SELECT id, username, password, role, is_verified FROM users WHERE username = ?", username).Scan(&user.ID, &user.Username, &user.Password, &user.Role, &user.IsVerified)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("user not found")
		}
		return nil, err
	}
	return user, nil
}
func (s *UserService) GetUserByID(userID int) (*models.User, error) {
	user := &models.User{}
	err := s.DB.QueryRow("SELECT id, username, password, role, is_verified FROM users WHERE id = ?", userID).Scan(&user.ID, &user.Username, &user.Password, &user.Role, &user.IsVerified)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("user not found")
		}
		return nil, err
	}
	return user, nil
}

func (s *UserService) CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func (s *UserService) GetAllUsers() ([]*models.User, error) {
	rows, err := s.DB.Query("SELECT id, username, role, is_verified FROM users")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []*models.User
	for rows.Next() {
		user := &models.User{}
		if err := rows.Scan(&user.ID, &user.Username, &user.Role, &user.IsVerified); err != nil {
			return nil, err
		}
		users = append(users, user)
	}
	return users, nil
}

func (s *UserService) GetUnverifiedUsers() ([]*models.User, error) {
	rows, err := s.DB.Query("SELECT id, username, role, is_verified FROM users WHERE is_verified = false")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []*models.User
	for rows.Next() {
		user := &models.User{}
		if err := rows.Scan(&user.ID, &user.Username, &user.Role, &user.IsVerified); err != nil {
			return nil, err
		}
		users = append(users, user)
	}
	return users, nil
}

func (s *UserService) VerifyUser(userID int) error {
	stmt, err := s.DB.Prepare("UPDATE users SET is_verified = true WHERE id = ?")
	if err != nil {
		return err
	}
	defer stmt.Close()

	_, err = stmt.Exec(userID)
	return err
}

func (s *UserService) DeleteUser(userID int) error {
	stmt, err := s.DB.Prepare("DELETE FROM users WHERE id = ?")
	if err != nil {
		return err
	}
	defer stmt.Close()

	_, err = stmt.Exec(userID)
	return err
}
