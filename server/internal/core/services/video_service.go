package services

import (
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"synclounge/server/internal/core/models"
	"synclounge/server/internal/transcoding"
)

// VideoService handles scanning and managing the media library.
type VideoService struct {
	mediaLibrary []models.Video
	mu           sync.RWMutex
}

// NewVideoService creates a new VideoService.
func NewVideoService() *VideoService {
	return &VideoService{
		mediaLibrary: make([]models.Video, 0),
	}
}

// ScanLibrary recursively scans the given directory for video files.
func (s *VideoService) ScanLibrary(mediaDir string) {
	log.Printf("Scanning media library in: %s", mediaDir)
	var videos []models.Video

	err := filepath.Walk(mediaDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && isVideoFile(info.Name()) {
			video, err := s.processVideoFile(path)
			if err != nil {
				log.Printf("Error processing video file %s: %v", path, err)
				return nil // Continue scanning
			}
			videos = append(videos, *video)
		}
		return nil
	})

	if err != nil {
		log.Printf("Error scanning media library: %v", err)
	}

	s.mu.Lock()
	s.mediaLibrary = videos
	s.mu.Unlock()

	log.Printf("Media library scan complete. Found %d videos.", len(videos))
}

// GetMediaLibrary returns the current media library.
func (s *VideoService) GetMediaLibrary() []models.Video {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.mediaLibrary
}

// GetVideoByID returns a video from the media library by its ID.
func (s *VideoService) GetVideoByID(id string) (*models.Video, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	for _, video := range s.mediaLibrary {
		if video.ID == id {
			return &video, true
		}
	}
	return nil, false
}
func (s *VideoService) processVideoFile(path string) (*models.Video, error) {
	// Generate a stable ID for the video based on its path
	hash := sha1.New()
	if _, err := io.WriteString(hash, path); err != nil {
		return nil, err
	}
	videoID := hex.EncodeToString(hash.Sum(nil))

	// Generate thumbnail
	thumbnailPath := filepath.Join("web/static/img/thumbnails", fmt.Sprintf("%s.jpg", videoID))
	if _, err := os.Stat(thumbnailPath); os.IsNotExist(err) {
		err := transcoding.GenerateThumbnail(path, thumbnailPath)
		if err != nil {
			return nil, fmt.Errorf("failed to generate thumbnail: %w", err)
		}
	}

	// In a real implementation, we would use ffprobe to get the duration.
	duration := 0

	video := &models.Video{
		ID:        videoID,
		Title:     filepath.Base(path),
		Path:      path,
		Duration:  time.Duration(duration),
		Thumbnail: "/" + thumbnailPath,
	}

	return video, nil
}

func isVideoFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".mp4", ".mkv", ".avi", ".mov", ".wmv", ".flv":
		return true
	default:
		return false
	}
}
