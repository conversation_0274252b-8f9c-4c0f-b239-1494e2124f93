package services

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"synclounge/server/internal/core/models"
	"synclounge/server/internal/core/websocket"
)

// RoomService handles business logic for rooms.
type RoomService struct {
	hub          *websocket.Hub
	videoService *VideoService
}

// NewRoomService creates a new RoomService.
func NewRoomService(hub *websocket.Hub, vs *VideoService) *RoomService {
	return &RoomService{
		hub:          hub,
		videoService: vs,
	}
}

// CreateRoom creates a new room and returns the room ID.
func (s *RoomService) CreateRoom(videoID string) (string, error) {
	roomID, err := generateUniqueID()
	if err != nil {
		return "", err
	}

	// Ensure the video exists before creating a room for it
	_, ok := s.videoService.GetVideoByID(videoID)
	if !ok {
		return "", fmt.Errorf("video with ID %s not found", videoID)
	}

	room := &models.Room{
		ID:               roomID,
		VideoID:          videoID,
		PlaybackPosition: 0,
		IsPlaying:        false,
	}

	// Pre-register the room in the hub
	s.hub.CreateRoom(room.ID, room.VideoID)

	return roomID, nil
}

// GetRoom retrieves details for a specific room.
func (s *RoomService) GetRoom(roomID string) (*models.RoomDetails, error) {
	wsRoom, ok := s.hub.GetRoom(roomID)
	if !ok {
		return nil, fmt.Errorf("room with ID %s not found", roomID)
	}

	video, ok := s.videoService.GetVideoByID(wsRoom.VideoID)
	if !ok {
		return nil, fmt.Errorf("could not find video with ID %s for room %s", wsRoom.VideoID, roomID)
	}

	details := &models.RoomDetails{
		ID:    wsRoom.ID,
		Video: video,
		// TODO: Get actual playback state from the hub/room
		PlaybackPosition: 0,
		IsPlaying:        false,
	}

	return details, nil
}

func generateUniqueID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
