package models

// Room represents a watch party room.
type Room struct {
	ID               string  `json:"id"`
	VideoID          string  `json:"videoId"`
	PlaybackPosition float64 `json:"playbackPosition"`
	IsPlaying        bool    `json:"isPlaying"`
	// Clients will be managed by the Hub to avoid circular dependencies.
}

// RoomDetails is a DTO that includes video details for API responses.
type RoomDetails struct {
	ID               string  `json:"id"`
	PlaybackPosition float64 `json:"playbackPosition"`
	IsPlaying        bool    `json:"isPlaying"`
	Video            *Video  `json:"video"`
}
