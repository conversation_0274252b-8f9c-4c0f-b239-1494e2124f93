package websocket

import (
	"sync"
)

// Room holds the state of a single watch party room in the context of websockets.
type Room struct {
	ID      string
	VideoID string
	Clients map[*Client]bool
	mu      sync.Mutex
}

// Hub maintains the set of active clients and broadcasts messages to the
// clients.
type Hub struct {
	// Registered clients.
	Rooms map[string]*Room

	// Inbound messages from the clients.
	Broadcast chan *Message

	// Register requests from the clients.
	Register chan *Client

	// Unregister requests from clients.
	Unregister chan *Client

	mu sync.Mutex
}

// NewHub creates a new Hub.
func NewHub() *Hub {
	return &Hub{
		Broadcast:  make(chan *Message),
		Register:   make(chan *Client),
		Unregister: make(chan *Client),
		Rooms:      make(map[string]*Room),
	}
}

// Run starts the hub's event loop.
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.Register:
			h.mu.Lock()
			if _, ok := h.Rooms[client.RoomID]; !ok {
				// This is now handled by Hub.CreateRoom
				// h.Rooms[client.RoomID] = &Room{
				// 	ID:      client.RoomID,
				// 	Clients: make(map[*Client]bool),
				// }
			}
			room := h.Rooms[client.RoomID]
			room.mu.Lock()
			room.Clients[client] = true
			room.mu.Unlock()
			h.mu.Unlock()
		case client := <-h.Unregister:
			h.mu.Lock()
			if room, ok := h.Rooms[client.RoomID]; ok {
				room.mu.Lock()
				if _, ok := room.Clients[client]; ok {
					delete(room.Clients, client)
					close(client.Send)
				}
				if len(room.Clients) == 0 {
					delete(h.Rooms, client.RoomID)
				}
				room.mu.Unlock()
			}
			h.mu.Unlock()
		case message := <-h.Broadcast:
			h.mu.Lock()
			if room, ok := h.Rooms[message.RoomID]; ok {
				room.mu.Lock()
				for client := range room.Clients {
					select {
					case client.Send <- message.Data:
					default:
						close(client.Send)
						delete(room.Clients, client)
					}
				}
				room.mu.Unlock()
			}
			h.mu.Unlock()
		}
	}
}

// GetRoom retrieves a room from the hub by its ID.
func (h *Hub) GetRoom(id string) (*Room, bool) {
	h.mu.Lock()
	defer h.mu.Unlock()
	room, ok := h.Rooms[id]
	return room, ok
}

// CreateRoom pre-registers a room in the hub before any clients join.
func (h *Hub) CreateRoom(id string, videoID string) {
	h.mu.Lock()
	defer h.mu.Unlock()
	if _, ok := h.Rooms[id]; !ok {
		h.Rooms[id] = &Room{
			ID:      id,
			VideoID: videoID,
			Clients: make(map[*Client]bool),
		}
	}
}
