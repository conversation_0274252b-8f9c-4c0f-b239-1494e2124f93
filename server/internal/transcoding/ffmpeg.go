package transcoding

import (
	"os"
	"os/exec"
	"path/filepath"
)

// GenerateThumbnail extracts a single frame from a video file to be used as a thumbnail.
func GenerateThumbnail(videoPath string, outputPath string) error {
	// Ensure the output directory exists
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return err
	}

	cmd := exec.Command("ffmpeg",
		"-i", videoPath,
		"-ss", "00:00:05", // Capture frame at 5 seconds
		"-vframes", "1",
		"-q:v", "2", // High quality
		"-vf", "scale=320:-1", // Scale width to 320px, maintain aspect ratio
		outputPath,
	)

	return cmd.Run()
}

// ActiveTranscodingSessions stores the command processes for active HLS transcoding sessions.
// The key is a unique identifier for the video stream, e.g., videoID.
var ActiveTranscodingSessions = make(map[string]*exec.Cmd)

// StartHlsTranscoding starts an FFmpeg process to transcode a video file to HLS format.
func StartHlsTranscoding(videoPath string, outputDir string) error {
	// Ensure the output directory exists
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return err
	}

	playlistPath := filepath.Join(outputDir, "stream.m3u8")
	cmd := exec.Command("ffmpeg",
		"-i", videoPath,
		"-c:v", "libx264",
		"-c:a", "aac",
		"-hls_time", "10",
		"-hls_list_size", "0",
		"-f", "hls",
		playlistPath,
	)

	// Start the command asynchronously
	err := cmd.Start()
	if err != nil {
		return err
	}

	// Store the command process to manage it later (e.g., to stop it)
	// For now, we'll use the output directory as a unique key.
	videoID := filepath.Base(outputDir)
	ActiveTranscodingSessions[videoID] = cmd

	// Optionally, you can wait for the command to finish in a separate goroutine
	// to handle cleanup and error logging.
	go func() {
		err := cmd.Wait()
		if err != nil {
			// Log error, e.g., log.Printf("FFmpeg command finished with error: %v", err)
		}
		// Clean up the session from the map
		delete(ActiveTranscodingSessions, videoID)
	}()

	return nil
}
