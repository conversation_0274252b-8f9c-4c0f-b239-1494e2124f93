package handlers

import (
	"log"
	"net/http"
	"synclounge/server/internal/core/services"
	"synclounge/server/internal/core/websocket"

	"github.com/gin-gonic/gin"
	gws "github.com/gorilla/websocket"
)

var upgrader = gws.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow all origins
	},
}

// RoomHandler holds dependencies for room handlers.
type RoomHandler struct {
	roomService *services.RoomService
	hub         *websocket.Hub
}

// NewRoomHandler creates a new RoomHandler.
func NewRoomHandler(rs *services.RoomService, hub *websocket.Hub) *RoomHandler {
	return &RoomHandler{
		roomService: rs,
		hub:         hub,
	}
}

// CreateRoomRequest is the request body for creating a room.
type CreateRoomRequest struct {
	VideoID string `json:"videoId" binding:"required"`
}

// CreateRoomHandler handles the creation of a new room.
func (h *<PERSON><PERSON>and<PERSON>) CreateRoomHandler(c *gin.Context) {
	var req CreateRoomRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	roomID, err := h.roomService.CreateRoom(req.VideoID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create room"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"id": roomID})
}

// GetRoomHandler handles fetching details for a single room.
func (h *RoomHandler) GetRoomHandler(c *gin.Context) {
	roomID := c.Param("roomId")
	roomDetails, err := h.roomService.GetRoom(roomID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, roomDetails)
}

// JoinRoomHandler handles WebSocket upgrade requests and client registration.
func (h *RoomHandler) JoinRoomHandler(c *gin.Context) {
	roomID := c.Param("roomID")

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Failed to upgrade connection for room %s: %v", roomID, err)
		return
	}

	client := &websocket.Client{
		Hub:    h.hub,
		Conn:   conn,
		Send:   make(chan []byte, 256),
		RoomID: roomID,
	}
	client.Hub.Register <- client

	// Allow collection of memory referenced by the caller by doing all work in
	// new goroutines.
	go client.WritePump()
	go client.ReadPump()
}

// ServeIndexHandler serves the main index.html page.
func (h *RoomHandler) ServeIndexHandler(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html.tmpl", nil)
}
