package handlers

import (
	"log"
	"net/http"
	"os"
	"path/filepath"
	"synclounge/server/internal/core/services"
	"synclounge/server/internal/transcoding"
	"time"

	"github.com/gin-gonic/gin"
)

// VideoHandler handles video-related API requests.
type VideoHandler struct {
	videoService *services.VideoService
}

// NewVideoHandler creates a new VideoHandler.
func NewVideoHandler(videoService *services.VideoService) *VideoHandler {
	return &VideoHandler{
		videoService: videoService,
	}
}

// GetMediaLibrary returns the media library as a JSON array.
func (h *VideoHandler) GetMediaLibrary(c *gin.Context) {
	library := h.videoService.GetMediaLibrary()
	c.JSON(http.StatusOK, library)
}

// StreamVideoHandler handles serving the HLS stream.
func (h *VideoHandler) StreamVideoHandler(c *gin.Context) {
	videoID := c.Param("videoID")

	// Look up the video path from the videoID
	video, found := h.videoService.GetVideoByID(videoID)
	if !found {
		log.Printf("Video not found: %s", videoID)
		c.String(http.StatusNotFound, "Video not found.")
		return
	}

	videoPath := video.Path
	outputDir := filepath.Join("hls_output", videoID)

	// Check if transcoding is already active for this videoID
	if _, ok := transcoding.ActiveTranscodingSessions[videoID]; !ok {
		log.Printf("Starting HLS transcoding for video: %s", videoID)
		// In a real app, you would get the full video path from your video service
		err := transcoding.StartHlsTranscoding(videoPath, outputDir)
		if err != nil {
			log.Printf("Error starting transcoding: %v", err)
			c.String(http.StatusInternalServerError, "Failed to start video stream.")
			return
		}
	}

	// Serve the master playlist
	playlistPath := filepath.Join(outputDir, "stream.m3u8")

	// Wait for the playlist file to be created by ffmpeg
	// This is a simple way to handle timing, a more robust solution might use file watching or a signaling mechanism.
	for i := 0; i < 10; i++ {
		if _, err := os.Stat(playlistPath); !os.IsNotExist(err) {
			break
		}
		time.Sleep(500 * time.Millisecond)
	}

	c.File(playlistPath)
}

// ServeHlsSegment serves the HLS video segments (.ts files).
func (h *VideoHandler) ServeHlsSegment(c *gin.Context) {
	videoID := c.Param("videoID")
	segment := c.Param("segment")
	segmentPath := filepath.Join("hls_output", videoID, segment)
	c.File(segmentPath)
}
